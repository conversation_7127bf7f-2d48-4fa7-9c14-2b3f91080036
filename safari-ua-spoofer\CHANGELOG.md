# 更新日志

## [1.0.0] - 2025-07-29

### 新增功能
- ✨ 初始版本发布
- 🍎 支持多种iPhone型号的Safari User-Agent伪装
  - iPhone 15 Pro (iOS 17.1)
  - iPhone 14 Pro (iOS 16.6)
  - iPhone 13 Pro (iOS 15.7)
  - iPhone 12 Pro (iOS 14.8)
  - iPhone SE (iOS 16.6)
- 🎯 HTTP请求头User-Agent修改
- 📱 JavaScript navigator属性伪装
- 🖥️ 屏幕尺寸和设备属性伪装
- 🌐 灵活的域名控制（所有网站/指定网站）
- 🚫 域名排除功能
- ✏️ 自定义User-Agent支持
- 🔧 实时配置更新
- 📊 内置测试页面
- 💾 配置持久化存储

### 技术特性
- 基于Chrome Extension Manifest V3
- 使用declarativeNetRequest API
- Content Script注入
- Chrome Storage API
- 现代化的弹窗界面

### 文件结构
- `manifest.json` - 插件清单文件
- `background.js` - 后台服务脚本
- `content.js` - 内容脚本
- `popup.html/css/js` - 弹窗界面
- `rules.json` - 默认规则配置
- `config.json` - 默认配置文件
- `test.html` - 测试页面
- `README.md` - 项目说明
- `INSTALL.md` - 安装指南
