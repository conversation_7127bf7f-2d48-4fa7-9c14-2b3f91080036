// Content script for UA Header Remover extension
// 处理一些边缘情况和提供额外的UA移除功能

(function() {
    'use strict';

    // 目标域名列表（从配置文件加载）
    let targetDomains = ['example.com', 'httpbin.org', 'test.com'];

    // 检查URL是否匹配目标域名
    function shouldRemoveUA(url) {
        if (!url) return false;

        try {
            const urlObj = new URL(url);
            const hostname = urlObj.hostname;

            return targetDomains.some(domain => {
                // 精确匹配
                if (hostname === domain) return true;
                // 子域名匹配
                if (hostname.endsWith('.' + domain)) return true;
                return false;
            });
        } catch (e) {
            return false;
        }
    }

    // 从扩展获取域名配置
    chrome.runtime.sendMessage({action: 'getDomains'}, (response) => {
        if (response && response.domains) {
            targetDomains = response.domains;
            console.log('Content script loaded target domains:', targetDomains);
        }
    });

    // 拦截XMLHttpRequest
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSetRequestHeader = XMLHttpRequest.prototype.setRequestHeader;

    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._url = url;
        return originalXHROpen.call(this, method, url, ...args);
    };

    XMLHttpRequest.prototype.setRequestHeader = function(header, value) {
        // 只对目标域名阻止设置User-Agent头
        if (header.toLowerCase() === 'user-agent' && shouldRemoveUA(this._url)) {
            console.log('Blocked User-Agent header in XMLHttpRequest for:', this._url);
            return;
        }
        return originalXHRSetRequestHeader.call(this, header, value);
    };

    // 拦截fetch API
    const originalFetch = window.fetch;
    window.fetch = function(input, init) {
        const url = typeof input === 'string' ? input : input.url;

        if (shouldRemoveUA(url) && init && init.headers) {
            // 如果是Headers对象
            if (init.headers instanceof Headers) {
                init.headers.delete('User-Agent');
                init.headers.delete('user-agent');
                init.headers.delete('User-agent');
            }
            // 如果是普通对象
            else if (typeof init.headers === 'object') {
                delete init.headers['User-Agent'];
                delete init.headers['user-agent'];
                delete init.headers['User-agent'];
            }
            console.log('Removed User-Agent header in fetch for:', url);
        }
        return originalFetch.call(this, input, init);
    };
    
    // 监听页面导航事件
    window.addEventListener('beforeunload', function() {
        console.log('Page unloading - UA remover active');
    });
    
    window.addEventListener('load', function() {
        console.log('Page loaded - UA remover active');
    });
    
    // 拦截动态创建的请求
    const observer = new MutationObserver(function(mutations) {
        mutations.forEach(function(mutation) {
            mutation.addedNodes.forEach(function(node) {
                if (node.nodeType === Node.ELEMENT_NODE) {
                    // 处理动态添加的script标签
                    if (node.tagName === 'SCRIPT' && node.src) {
                        console.log('Dynamic script detected:', node.src);
                    }
                    // 处理动态添加的img标签
                    if (node.tagName === 'IMG' && node.src) {
                        console.log('Dynamic image detected:', node.src);
                    }
                }
            });
        });
    });
    
    observer.observe(document.body || document.documentElement, {
        childList: true,
        subtree: true
    });
    
    console.log('UA Header Remover content script loaded');
})();
