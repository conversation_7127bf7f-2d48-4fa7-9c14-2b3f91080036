// Safari iPhone UA Spoofer Background Script
// 伪装浏览器User-Agent为苹果手机Safari浏览器

// 不同版本的iPhone Safari User-Agent字符串
const SAFARI_USER_AGENTS = {
  'iphone_15_pro': 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
  'iphone_14_pro': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1',
  'iphone_13_pro': 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_7 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.6 Mobile/15E148 Safari/604.1',
  'iphone_12_pro': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_8 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1 Mobile/15E148 Safari/604.1',
  'iphone_se': 'Mozilla/5.0 (iPhone; CPU iPhone OS 16_6 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/16.6 Mobile/15E148 Safari/604.1'
};

// 默认配置
const DEFAULT_CONFIG = {
  enabled: true,
  selectedUA: 'iphone_15_pro',
  customUA: '',
  useCustomUA: false,
  targetDomains: [], // 空数组表示所有域名
  excludeDomains: [] // 排除的域名
};

let currentConfig = DEFAULT_CONFIG;

// 加载配置
async function loadConfig() {
  try {
    const result = await chrome.storage.sync.get('safariUAConfig');
    if (result.safariUAConfig) {
      currentConfig = { ...DEFAULT_CONFIG, ...result.safariUAConfig };
    }
    console.log('Config loaded:', currentConfig);
  } catch (error) {
    console.error('Failed to load config:', error);
    currentConfig = DEFAULT_CONFIG;
  }
}

// 保存配置
async function saveConfig(config) {
  try {
    currentConfig = { ...currentConfig, ...config };
    await chrome.storage.sync.set({ safariUAConfig: currentConfig });
    console.log('Config saved:', currentConfig);
  } catch (error) {
    console.error('Failed to save config:', error);
  }
}

// 获取当前使用的User-Agent
function getCurrentUserAgent() {
  if (currentConfig.useCustomUA && currentConfig.customUA) {
    return currentConfig.customUA;
  }
  return SAFARI_USER_AGENTS[currentConfig.selectedUA] || SAFARI_USER_AGENTS.iphone_15_pro;
}

// 更新动态规则
async function updateDynamicRules() {
  try {
    // 清除所有现有规则
    const existingRules = await chrome.declarativeNetRequest.getDynamicRules();
    const ruleIds = existingRules.map(rule => rule.id);
    
    if (ruleIds.length > 0) {
      await chrome.declarativeNetRequest.updateDynamicRules({
        removeRuleIds: ruleIds
      });
    }

    // 如果插件未启用，不添加新规则
    if (!currentConfig.enabled) {
      console.log('Safari UA Spoofer disabled');
      return;
    }

    const userAgent = getCurrentUserAgent();
    console.log('Using User-Agent:', userAgent);

    // 构建规则条件
    let condition = {
      resourceTypes: [
        "main_frame",
        "sub_frame", 
        "stylesheet",
        "script",
        "image",
        "font",
        "object",
        "xmlhttprequest",
        "ping",
        "csp_report",
        "media",
        "websocket",
        "webtransport",
        "webbundle",
        "other"
      ]
    };

    // 如果指定了目标域名，只对这些域名生效
    if (currentConfig.targetDomains && currentConfig.targetDomains.length > 0) {
      condition.requestDomains = currentConfig.targetDomains;
    }

    // 如果指定了排除域名，排除这些域名
    if (currentConfig.excludeDomains && currentConfig.excludeDomains.length > 0) {
      condition.excludedRequestDomains = currentConfig.excludeDomains;
    }

    // 添加新规则
    await chrome.declarativeNetRequest.updateDynamicRules({
      addRules: [
        {
          id: 1,
          priority: 100,
          action: {
            type: "modifyHeaders",
            requestHeaders: [
              {
                header: "User-Agent",
                operation: "set",
                value: userAgent
              }
            ]
          },
          condition: condition
        }
      ]
    });

    console.log('Dynamic rules updated successfully');
  } catch (error) {
    console.error('Failed to update dynamic rules:', error);
  }
}

// 扩展安装时初始化
chrome.runtime.onInstalled.addListener(async () => {
  console.log('Safari iPhone UA Spoofer installed');
  await loadConfig();
  await updateDynamicRules();
});

// 扩展启动时初始化
chrome.runtime.onStartup.addListener(async () => {
  console.log('Safari iPhone UA Spoofer started');
  await loadConfig();
  await updateDynamicRules();
});

// 处理来自popup的消息
chrome.runtime.onMessage.addListener(async (request, sender, sendResponse) => {
  try {
    switch (request.action) {
      case 'getConfig':
        sendResponse({ success: true, config: currentConfig });
        break;
        
      case 'updateConfig':
        await saveConfig(request.config);
        await updateDynamicRules();
        sendResponse({ success: true });
        break;
        
      case 'getCurrentUA':
        sendResponse({ success: true, userAgent: getCurrentUserAgent() });
        break;
        
      case 'getAvailableUAs':
        sendResponse({ success: true, userAgents: SAFARI_USER_AGENTS });
        break;
        
      default:
        sendResponse({ success: false, error: 'Unknown action' });
    }
  } catch (error) {
    console.error('Error handling message:', error);
    sendResponse({ success: false, error: error.message });
  }
  
  return true; // 保持消息通道开放
});

// 调试：监听规则匹配
chrome.declarativeNetRequest.onRuleMatchedDebug.addListener((info) => {
  console.log('Rule matched:', info);
});
