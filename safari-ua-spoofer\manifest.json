{"manifest_version": 3, "name": "Safari iPhone UA Spoofer", "version": "1.0.0", "description": "伪装浏览器User-Agent为苹果手机Safari浏览器", "permissions": ["declarativeNetRequest", "declarativeNetRequestWithHostAccess", "declarativeNetRequestFeedback", "storage", "activeTab"], "host_permissions": ["<all_urls>"], "background": {"service_worker": "background.js"}, "content_scripts": [{"matches": ["<all_urls>"], "js": ["content.js"], "run_at": "document_start", "all_frames": true}], "action": {"default_popup": "popup.html", "default_title": "Safari iPhone UA Spoofer"}, "declarative_net_request": {"rule_resources": [{"id": "safari_ua_rules", "enabled": true, "path": "rules.json"}]}, "web_accessible_resources": [{"resources": ["config.json"], "matches": ["<all_urls>"]}]}