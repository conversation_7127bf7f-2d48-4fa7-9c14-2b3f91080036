// ================== 悬浮窗模块 ==================
class FloatingWindow {
    constructor() {
        this.element = null;
    }

    create() {
        if (document.getElementById('auto-register-float')) return; // 防止多次注入

        this.element = document.createElement('div');
        this.element.id = 'auto-register-float';
        this.element.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            background: transparent;
            padding: 5px;
            user-select: none;
            transition: opacity 0.3s ease;
        `;

        const button = document.createElement('button');
        button.textContent = '验证';
        button.style.cssText = `
            background: #4CAF50;
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 16px;
            font-family: 'Arial', sans-serif;
            cursor: pointer;
            padding: 10px 20px;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
            transition: background 0.3s ease, transform 0.1s ease;
        `;
        button.addEventListener('mouseover', () => {
            button.style.background = '#45a049';
            button.style.transform = 'translateY(-2px)';
        });
        button.addEventListener('mouseout', () => {
            button.style.background = '#4CAF50';
            button.style.transform = 'translateY(0)';
        });

        this.element.appendChild(button);
        document.body.appendChild(this.element);

        // 点击按钮触发验证流程
        button.addEventListener('click', () => {
            verificationFlow.execute();
        });
    }
}

// ================== 验证流程模块 ==================
class VerificationFlow {
    // 查找验证链接的正则
    verifyLinkReg = /https:\/\/lpts\.me\/a\/[\w\-]+/g;

    execute() {
        // 使用正则在页面文本中查找
        const bodyText = document.body.innerText || document.body.textContent || '';
        const matches = bodyText.match(this.verifyLinkReg);

        if (matches && matches.length > 0) {
            // 跳转第一个验证链接
            window.open(matches[0], '_self');
        } else {
            // 没找到就刷新页面
            location.reload();
        }
    }
}

const floatingWindow = new FloatingWindow();
const verificationFlow = new VerificationFlow();

window.addEventListener('load', () => {
    floatingWindow.create();
});