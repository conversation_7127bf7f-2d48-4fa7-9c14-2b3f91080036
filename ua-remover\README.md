# UA Header Remover 浏览器扩展

这是一个智能的浏览器扩展，用于去掉**特定域名**HTTP请求中的User-Agent头。

## 功能特点

- **智能域名过滤**：只对指定域名移除User-Agent头，其他域名保持正常
- **灵活配置**：通过 `domains.json` 文件轻松管理目标域名列表
- **精确匹配**：支持精确域名匹配和子域名匹配
- 无需用户界面，安装后自动工作
- 支持所有类型的网络请求（页面、脚本、图片、XHR等）
- 使用Chrome Manifest V3标准
- **增强版功能**：
  - 多重规则确保覆盖所有大小写变体
  - 动态规则更新，提高可靠性
  - 内容脚本拦截JavaScript发起的请求
  - 支持新窗口/新标签页场景
  - 高优先级规则（priority: 100）

## 安装方法

1. 打开Chrome浏览器
2. 进入扩展管理页面 (chrome://extensions/)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择包含此扩展文件的文件夹

## 文件说明

- `manifest.json` - 扩展配置文件
- `background.js` - 后台脚本，包含动态规则管理和域名配置加载
- `content.js` - 内容脚本，拦截JavaScript请求（支持域名过滤）
- `rules.json` - 网络请求修改规则（多重规则，基于域名）
- `domains.json` - **域名配置文件**，定义需要移除UA头的目标域名
- `test.html` - 基础测试页面
- `domain-test.html` - **域名特定测试页面**，测试不同域名的UA移除效果
- `README.md` - 说明文档

## 工作原理

扩展使用Chrome的declarativeNetRequest API来拦截和修改网络请求，自动移除所有请求中的User-Agent头部信息。

## 域名配置

### 编辑目标域名

编辑 `domains.json` 文件来配置需要移除UA头的域名：

```json
{
  "targetDomains": [
    "example.com",        // 精确匹配 example.com
    "httpbin.org",        // 精确匹配 httpbin.org
    "api.example.com",    // 精确匹配 api.example.com
    "test.com"            // 精确匹配 test.com 及其子域名
  ]
}
```

### 域名匹配规则

- **精确匹配**：`example.com` 只匹配 `example.com`
- **子域名匹配**：`example.com` 也会匹配 `sub.example.com`、`api.example.com` 等
- **不支持通配符**：不能使用 `*.example.com` 语法

## 测试方法

1. **域名特定测试**：打开 `domain-test.html` 文件，测试不同域名的UA移除效果
2. **基础测试**：打开 `test.html` 文件，运行各种基础测试
3. **新窗口测试**：点击链接或按钮打开新窗口/标签页进行测试
4. **开发者工具**：F12 → Network标签页查看实际请求头
5. **在线验证**：访问 `https://httpbin.org/headers` 查看结果（httpbin.org在默认配置中）

## 故障排除

如果在某些场景下失效：

1. **重新加载扩展**：在 `chrome://extensions/` 中点击刷新按钮
2. **检查控制台**：F12 → Console查看是否有错误信息
3. **清除缓存**：清除浏览器缓存后重试
4. **重启浏览器**：完全关闭并重新打开Chrome

## 注意事项

- 移除User-Agent头可能导致某些网站无法正常工作
- 建议在需要时临时禁用扩展
- 此扩展仅用于学习和测试目的
- 新版本增强了对新窗口/新标签页的支持
