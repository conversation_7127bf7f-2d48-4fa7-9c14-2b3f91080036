# Safari iPhone UA Spoofer

一个Chrome浏览器插件，用于将浏览器的User-Agent伪装成苹果手机的Safari浏览器。

## 功能特性

- 🍎 **多种iPhone型号支持**: 支持iPhone 15 Pro、14 Pro、13 Pro、12 Pro、SE等多种型号
- 🎯 **精确伪装**: 不仅修改HTTP请求头，还修改JavaScript中的navigator属性
- 🌐 **灵活域名控制**: 可以选择对所有网站生效或仅对指定网站生效
- 🚫 **排除域名**: 可以排除特定域名，避免在某些网站上使用伪装
- ✏️ **自定义UA**: 支持使用自定义User-Agent字符串
- 🔧 **实时配置**: 无需重启浏览器即可应用设置

## 安装方法

1. 下载或克隆此项目到本地
2. 打开Chrome浏览器，进入扩展程序管理页面 (`chrome://extensions/`)
3. 开启"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择项目文件夹 `safari-ua-spoofer`

## 使用方法

1. 安装插件后，点击浏览器工具栏中的插件图标
2. 在弹出的设置面板中：
   - 开启/关闭UA伪装功能
   - 选择要伪装的iPhone型号
   - 设置目标域名（可选）
   - 排除特定域名（可选）
3. 点击"保存设置"应用配置
4. 点击"测试UA"可以打开测试页面验证效果

## 支持的iPhone型号

- iPhone 15 Pro (iOS 17.1)
- iPhone 14 Pro (iOS 16.6)  
- iPhone 13 Pro (iOS 15.7)
- iPhone 12 Pro (iOS 14.8)
- iPhone SE (iOS 16.6)

## 技术实现

- 使用Chrome Extension Manifest V3
- 通过`declarativeNetRequest` API修改HTTP请求头
- 通过Content Script修改JavaScript中的navigator属性
- 支持实时配置更新，无需重启浏览器

## 注意事项

- 此插件仅用于开发测试目的
- 请遵守网站的使用条款和相关法律法规
- 某些网站可能有其他检测机制，仅修改User-Agent可能不足以完全伪装

## 许可证

MIT License
