// Safari iPhone UA Spoofer Content Script
// 在页面中进一步伪装User-Agent相关属性

(function() {
  'use strict';

  // iPhone Safari的navigator属性
  const IPHONE_NAVIGATOR_PROPS = {
    userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    platform: 'iPhone',
    vendor: 'Apple Computer, Inc.',
    vendorSub: '',
    productSub: '20030107',
    appName: 'Netscape',
    appVersion: '5.0 (iPhone; CPU iPhone OS 17_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Mobile/15E148 Safari/604.1',
    appCodeName: 'Mozilla',
    maxTouchPoints: 5,
    hardwareConcurrency: 6
  };

  // iPhone的screen属性
  const IPHONE_SCREEN_PROPS = {
    width: 393,
    height: 852,
    availWidth: 393,
    availHeight: 852,
    colorDepth: 24,
    pixelDepth: 24
  };

  // 获取当前配置并应用伪装
  async function applySpoof() {
    try {
      // 从background script获取当前配置
      const response = await chrome.runtime.sendMessage({ action: 'getConfig' });
      
      if (!response.success || !response.config.enabled) {
        return;
      }

      const config = response.config;
      
      // 获取当前使用的User-Agent
      const uaResponse = await chrome.runtime.sendMessage({ action: 'getCurrentUA' });
      if (uaResponse.success) {
        IPHONE_NAVIGATOR_PROPS.userAgent = uaResponse.userAgent;
        IPHONE_NAVIGATOR_PROPS.appVersion = uaResponse.userAgent;
      }

      // 重写navigator属性
      Object.defineProperty(navigator, 'userAgent', {
        get: () => IPHONE_NAVIGATOR_PROPS.userAgent,
        configurable: true
      });

      Object.defineProperty(navigator, 'platform', {
        get: () => IPHONE_NAVIGATOR_PROPS.platform,
        configurable: true
      });

      Object.defineProperty(navigator, 'vendor', {
        get: () => IPHONE_NAVIGATOR_PROPS.vendor,
        configurable: true
      });

      Object.defineProperty(navigator, 'vendorSub', {
        get: () => IPHONE_NAVIGATOR_PROPS.vendorSub,
        configurable: true
      });

      Object.defineProperty(navigator, 'productSub', {
        get: () => IPHONE_NAVIGATOR_PROPS.productSub,
        configurable: true
      });

      Object.defineProperty(navigator, 'appName', {
        get: () => IPHONE_NAVIGATOR_PROPS.appName,
        configurable: true
      });

      Object.defineProperty(navigator, 'appVersion', {
        get: () => IPHONE_NAVIGATOR_PROPS.appVersion,
        configurable: true
      });

      Object.defineProperty(navigator, 'appCodeName', {
        get: () => IPHONE_NAVIGATOR_PROPS.appCodeName,
        configurable: true
      });

      Object.defineProperty(navigator, 'maxTouchPoints', {
        get: () => IPHONE_NAVIGATOR_PROPS.maxTouchPoints,
        configurable: true
      });

      Object.defineProperty(navigator, 'hardwareConcurrency', {
        get: () => IPHONE_NAVIGATOR_PROPS.hardwareConcurrency,
        configurable: true
      });

      // 重写screen属性
      Object.defineProperty(screen, 'width', {
        get: () => IPHONE_SCREEN_PROPS.width,
        configurable: true
      });

      Object.defineProperty(screen, 'height', {
        get: () => IPHONE_SCREEN_PROPS.height,
        configurable: true
      });

      Object.defineProperty(screen, 'availWidth', {
        get: () => IPHONE_SCREEN_PROPS.availWidth,
        configurable: true
      });

      Object.defineProperty(screen, 'availHeight', {
        get: () => IPHONE_SCREEN_PROPS.availHeight,
        configurable: true
      });

      // 添加移动设备特有的事件支持
      if (!window.ontouchstart) {
        window.ontouchstart = null;
        window.ontouchmove = null;
        window.ontouchend = null;
      }

      // 伪装设备像素比
      if (window.devicePixelRatio !== 3) {
        Object.defineProperty(window, 'devicePixelRatio', {
          get: () => 3,
          configurable: true
        });
      }

      // 添加移动Safari特有的CSS媒体查询支持
      const style = document.createElement('style');
      style.textContent = `
        @media (-webkit-device-pixel-ratio: 3) {
          /* iPhone显示屏样式 */
        }
      `;
      document.head.appendChild(style);

      console.log('Safari iPhone UA spoofing applied');
      
    } catch (error) {
      console.error('Failed to apply Safari iPhone spoofing:', error);
    }
  }

  // 在页面加载前应用伪装
  if (document.readyState === 'loading') {
    applySpoof();
  } else {
    // 如果页面已经加载，立即应用
    applySpoof();
  }

  // 监听来自background的消息
  chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
    if (request.action === 'reapplySpoof') {
      applySpoof();
      sendResponse({ success: true });
    }
  });

})();
