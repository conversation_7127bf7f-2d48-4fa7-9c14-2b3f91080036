<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safari iPhone UA Spoofer - 测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(135deg, #007AFF, #5856D6);
            color: white;
            border-radius: 10px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            border-left: 4px solid #007AFF;
        }
        .test-item {
            margin: 10px 0;
            padding: 10px;
            background: white;
            border-radius: 5px;
            border: 1px solid #e9ecef;
        }
        .label {
            font-weight: bold;
            color: #495057;
            margin-right: 10px;
        }
        .value {
            font-family: monospace;
            background: #e9ecef;
            padding: 2px 6px;
            border-radius: 3px;
            word-break: break-all;
        }
        .success {
            color: #28a745;
        }
        .warning {
            color: #ffc107;
        }
        .error {
            color: #dc3545;
        }
        .refresh-btn {
            background: #007AFF;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .refresh-btn:hover {
            background: #0056CC;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🍎 Safari iPhone UA Spoofer</h1>
        <p>User-Agent 伪装效果测试页面</p>
        <button class="refresh-btn" onclick="location.reload()">刷新测试</button>
    </div>

    <div class="test-section">
        <h2>HTTP 请求头信息</h2>
        <div class="test-item">
            <span class="label">User-Agent:</span>
            <span class="value" id="httpUserAgent">检测中...</span>
        </div>
    </div>

    <div class="test-section">
        <h2>JavaScript Navigator 属性</h2>
        <div class="test-item">
            <span class="label">navigator.userAgent:</span>
            <span class="value" id="jsUserAgent"></span>
        </div>
        <div class="test-item">
            <span class="label">navigator.platform:</span>
            <span class="value" id="jsPlatform"></span>
        </div>
        <div class="test-item">
            <span class="label">navigator.vendor:</span>
            <span class="value" id="jsVendor"></span>
        </div>
        <div class="test-item">
            <span class="label">navigator.appVersion:</span>
            <span class="value" id="jsAppVersion"></span>
        </div>
        <div class="test-item">
            <span class="label">navigator.maxTouchPoints:</span>
            <span class="value" id="jsMaxTouchPoints"></span>
        </div>
    </div>

    <div class="test-section">
        <h2>设备信息</h2>
        <div class="test-item">
            <span class="label">screen.width:</span>
            <span class="value" id="screenWidth"></span>
        </div>
        <div class="test-item">
            <span class="label">screen.height:</span>
            <span class="value" id="screenHeight"></span>
        </div>
        <div class="test-item">
            <span class="label">devicePixelRatio:</span>
            <span class="value" id="devicePixelRatio"></span>
        </div>
        <div class="test-item">
            <span class="label">触摸支持:</span>
            <span class="value" id="touchSupport"></span>
        </div>
    </div>

    <div class="test-section">
        <h2>伪装效果评估</h2>
        <div class="test-item">
            <span class="label">整体评估:</span>
            <span class="value" id="overallAssessment"></span>
        </div>
        <div class="test-item">
            <span class="label">建议:</span>
            <span class="value" id="suggestions"></span>
        </div>
    </div>

    <script>
        // 获取HTTP User-Agent (通过API)
        async function getHttpUserAgent() {
            try {
                const response = await fetch('https://httpbin.org/user-agent');
                const data = await response.json();
                return data['user-agent'];
            } catch (error) {
                return '无法获取HTTP User-Agent';
            }
        }

        // 检查是否为iPhone Safari UA
        function isiPhoneSafari(ua) {
            return ua.includes('iPhone') && 
                   ua.includes('Safari') && 
                   ua.includes('Mobile') &&
                   ua.includes('AppleWebKit');
        }

        // 更新页面信息
        async function updateInfo() {
            // JavaScript Navigator 属性
            document.getElementById('jsUserAgent').textContent = navigator.userAgent;
            document.getElementById('jsPlatform').textContent = navigator.platform;
            document.getElementById('jsVendor').textContent = navigator.vendor;
            document.getElementById('jsAppVersion').textContent = navigator.appVersion;
            document.getElementById('jsMaxTouchPoints').textContent = navigator.maxTouchPoints;

            // 设备信息
            document.getElementById('screenWidth').textContent = screen.width;
            document.getElementById('screenHeight').textContent = screen.height;
            document.getElementById('devicePixelRatio').textContent = window.devicePixelRatio;
            document.getElementById('touchSupport').textContent = 'ontouchstart' in window ? '支持' : '不支持';

            // HTTP User-Agent
            const httpUA = await getHttpUserAgent();
            document.getElementById('httpUserAgent').textContent = httpUA;

            // 评估伪装效果
            const jsUA = navigator.userAgent;
            const jsIsiPhone = isiPhoneSafari(jsUA);
            const httpIsiPhone = isiPhoneSafari(httpUA);
            
            let assessment = '';
            let suggestions = '';
            
            if (jsIsiPhone && httpIsiPhone) {
                assessment = '<span class="success">✅ 伪装成功</span>';
                suggestions = '所有检测项都显示为iPhone Safari，伪装效果良好！';
            } else if (jsIsiPhone || httpIsiPhone) {
                assessment = '<span class="warning">⚠️ 部分伪装</span>';
                suggestions = '部分属性已伪装，但可能还有其他检测点需要注意。';
            } else {
                assessment = '<span class="error">❌ 伪装失败</span>';
                suggestions = '未检测到iPhone Safari特征，请检查插件设置。';
            }
            
            document.getElementById('overallAssessment').innerHTML = assessment;
            document.getElementById('suggestions').textContent = suggestions;
        }

        // 页面加载完成后更新信息
        document.addEventListener('DOMContentLoaded', updateInfo);
    </script>
</body>
</html>
