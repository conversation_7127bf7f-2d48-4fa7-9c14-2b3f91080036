// Safari iPhone UA Spoofer Popup Script

document.addEventListener('DOMContentLoaded', async () => {
    // 获取DOM元素
    const enableSpoof = document.getElementById('enableSpoof');
    const uaSelect = document.getElementById('uaSelect');
    const useCustomUA = document.getElementById('useCustomUA');
    const customUA = document.getElementById('customUA');
    const currentUA = document.getElementById('currentUA');
    const allDomains = document.getElementById('allDomains');
    const specificDomains = document.getElementById('specificDomains');
    const targetDomains = document.getElementById('targetDomains');
    const excludeDomains = document.getElementById('excludeDomains');
    const saveBtn = document.getElementById('saveBtn');
    const resetBtn = document.getElementById('resetBtn');
    const testBtn = document.getElementById('testBtn');
    const status = document.getElementById('status');

    let currentConfig = {};

    // 显示状态消息
    function showStatus(message, type = 'info') {
        status.textContent = message;
        status.className = `status ${type}`;
        status.style.display = 'block';
        
        setTimeout(() => {
            status.style.display = 'none';
        }, 3000);
    }

    // 加载配置
    async function loadConfig() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getConfig' });
            if (response.success) {
                currentConfig = response.config;
                updateUI();
                updateCurrentUA();
            } else {
                showStatus('加载配置失败', 'error');
            }
        } catch (error) {
            console.error('Failed to load config:', error);
            showStatus('加载配置失败', 'error');
        }
    }

    // 更新UI
    function updateUI() {
        enableSpoof.checked = currentConfig.enabled;
        uaSelect.value = currentConfig.selectedUA || 'iphone_15_pro';
        useCustomUA.checked = currentConfig.useCustomUA;
        customUA.value = currentConfig.customUA || '';
        customUA.disabled = !currentConfig.useCustomUA;
        
        // 域名设置
        if (currentConfig.targetDomains && currentConfig.targetDomains.length > 0) {
            specificDomains.checked = true;
            allDomains.checked = false;
            targetDomains.disabled = false;
            targetDomains.value = currentConfig.targetDomains.join('\n');
        } else {
            allDomains.checked = true;
            specificDomains.checked = false;
            targetDomains.disabled = true;
        }
        
        excludeDomains.value = (currentConfig.excludeDomains || []).join('\n');
    }

    // 更新当前User-Agent显示
    async function updateCurrentUA() {
        try {
            const response = await chrome.runtime.sendMessage({ action: 'getCurrentUA' });
            if (response.success) {
                currentUA.textContent = response.userAgent;
            }
        } catch (error) {
            console.error('Failed to get current UA:', error);
        }
    }

    // 保存配置
    async function saveConfig() {
        try {
            const config = {
                enabled: enableSpoof.checked,
                selectedUA: uaSelect.value,
                useCustomUA: useCustomUA.checked,
                customUA: customUA.value.trim(),
                targetDomains: specificDomains.checked ? 
                    targetDomains.value.split('\n').map(d => d.trim()).filter(d => d) : [],
                excludeDomains: excludeDomains.value.split('\n').map(d => d.trim()).filter(d => d)
            };

            const response = await chrome.runtime.sendMessage({ 
                action: 'updateConfig', 
                config: config 
            });
            
            if (response.success) {
                currentConfig = { ...currentConfig, ...config };
                updateCurrentUA();
                showStatus('设置已保存', 'success');
            } else {
                showStatus('保存失败', 'error');
            }
        } catch (error) {
            console.error('Failed to save config:', error);
            showStatus('保存失败', 'error');
        }
    }

    // 重置配置
    function resetConfig() {
        const defaultConfig = {
            enabled: true,
            selectedUA: 'iphone_15_pro',
            useCustomUA: false,
            customUA: '',
            targetDomains: [],
            excludeDomains: []
        };
        
        currentConfig = defaultConfig;
        updateUI();
        updateCurrentUA();
        showStatus('已重置为默认设置', 'info');
    }

    // 测试User-Agent
    function testUA() {
        const testUrl = 'https://httpbin.org/user-agent';
        chrome.tabs.create({ url: testUrl });
        showStatus('已打开测试页面', 'info');
    }

    // 事件监听器
    enableSpoof.addEventListener('change', () => {
        if (!enableSpoof.checked) {
            showStatus('UA伪装已禁用', 'info');
        }
    });

    uaSelect.addEventListener('change', updateCurrentUA);

    useCustomUA.addEventListener('change', () => {
        customUA.disabled = !useCustomUA.checked;
        if (useCustomUA.checked) {
            customUA.focus();
        }
        updateCurrentUA();
    });

    customUA.addEventListener('input', updateCurrentUA);

    allDomains.addEventListener('change', () => {
        if (allDomains.checked) {
            targetDomains.disabled = true;
            targetDomains.value = '';
        }
    });

    specificDomains.addEventListener('change', () => {
        if (specificDomains.checked) {
            targetDomains.disabled = false;
            targetDomains.focus();
        }
    });

    saveBtn.addEventListener('click', saveConfig);
    resetBtn.addEventListener('click', resetConfig);
    testBtn.addEventListener('click', testUA);

    // 初始化
    await loadConfig();
});
