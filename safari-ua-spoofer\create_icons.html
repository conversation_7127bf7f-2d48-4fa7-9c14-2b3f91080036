<!DOCTYPE html>
<html>
<head>
    <title>创建插件图标</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        canvas { border: 1px solid #ccc; margin: 10px; }
        .icon-set { margin: 20px 0; }
        button { padding: 10px 20px; margin: 5px; background: #007AFF; color: white; border: none; border-radius: 5px; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Safari iPhone UA Spoofer 图标生成器</h1>
    <p>点击下面的按钮生成并下载图标文件：</p>
    
    <div class="icon-set">
        <h3>预览：</h3>
        <canvas id="preview16" width="16" height="16"></canvas>
        <canvas id="preview32" width="32" height="32"></canvas>
        <canvas id="preview48" width="48" height="48"></canvas>
        <canvas id="preview128" width="128" height="128"></canvas>
    </div>
    
    <div>
        <button onclick="downloadIcon(16)">下载 icon16.png</button>
        <button onclick="downloadIcon(32)">下载 icon32.png</button>
        <button onclick="downloadIcon(48)">下载 icon48.png</button>
        <button onclick="downloadIcon(128)">下载 icon128.png</button>
        <button onclick="downloadAllIcons()">下载所有图标</button>
    </div>
    
    <div style="margin-top: 20px;">
        <h3>使用说明：</h3>
        <ol>
            <li>点击对应按钮下载图标文件</li>
            <li>将下载的图标文件放入 <code>safari-ua-spoofer/icons/</code> 文件夹</li>
            <li>恢复 manifest.json 中的图标配置（如果需要）</li>
        </ol>
    </div>

    <script>
        // 绘制图标
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const centerX = size / 2;
            const centerY = size / 2;
            const radius = size * 0.4;
            
            // 清除画布
            ctx.clearRect(0, 0, size, size);
            
            // 绘制蓝色圆形背景
            ctx.fillStyle = '#007AFF';
            ctx.beginPath();
            ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
            ctx.fill();
            
            // 绘制白色的手机图标
            const phoneWidth = size * 0.25;
            const phoneHeight = size * 0.4;
            const phoneX = centerX - phoneWidth / 2;
            const phoneY = centerY - phoneHeight / 2;
            
            ctx.fillStyle = 'white';
            ctx.fillRect(phoneX, phoneY, phoneWidth, phoneHeight);
            
            // 绘制屏幕
            const screenMargin = size * 0.03;
            ctx.fillStyle = '#007AFF';
            ctx.fillRect(
                phoneX + screenMargin, 
                phoneY + screenMargin * 2, 
                phoneWidth - screenMargin * 2, 
                phoneHeight - screenMargin * 4
            );
            
            // 绘制Home按钮
            const buttonRadius = size * 0.02;
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(centerX, phoneY + phoneHeight - screenMargin, buttonRadius, 0, 2 * Math.PI);
            ctx.fill();
        }
        
        // 初始化预览
        function initPreviews() {
            [16, 32, 48, 128].forEach(size => {
                const canvas = document.getElementById(`preview${size}`);
                drawIcon(canvas, size);
            });
        }
        
        // 下载图标
        function downloadIcon(size) {
            const canvas = document.createElement('canvas');
            canvas.width = size;
            canvas.height = size;
            drawIcon(canvas, size);
            
            canvas.toBlob(blob => {
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `icon${size}.png`;
                a.click();
                URL.revokeObjectURL(url);
            });
        }
        
        // 下载所有图标
        function downloadAllIcons() {
            [16, 32, 48, 128].forEach((size, index) => {
                setTimeout(() => downloadIcon(size), index * 500);
            });
        }
        
        // 页面加载完成后初始化
        window.onload = initPreviews;
    </script>
</body>
</html>
