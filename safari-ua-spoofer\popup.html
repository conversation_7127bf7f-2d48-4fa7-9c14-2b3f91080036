<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Safari iPhone UA Spoofer</title>
    <link rel="stylesheet" href="popup.css">
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🍎 Safari iPhone UA</h1>
            <p>伪装为苹果手机Safari浏览器</p>
        </div>

        <div class="main-content">
            <!-- 启用/禁用开关 -->
            <div class="setting-group">
                <div class="setting-item">
                    <label class="switch">
                        <input type="checkbox" id="enableSpoof" checked>
                        <span class="slider"></span>
                    </label>
                    <span class="setting-label">启用UA伪装</span>
                </div>
            </div>

            <!-- User-Agent选择 -->
            <div class="setting-group">
                <label for="uaSelect" class="group-label">选择iPhone型号:</label>
                <select id="uaSelect" class="select-input">
                    <option value="iphone_15_pro">iPhone 15 Pro (iOS 17.1)</option>
                    <option value="iphone_14_pro">iPhone 14 Pro (iOS 16.6)</option>
                    <option value="iphone_13_pro">iPhone 13 Pro (iOS 15.7)</option>
                    <option value="iphone_12_pro">iPhone 12 Pro (iOS 14.8)</option>
                    <option value="iphone_se">iPhone SE (iOS 16.6)</option>
                </select>
            </div>

            <!-- 自定义User-Agent -->
            <div class="setting-group">
                <div class="setting-item">
                    <input type="checkbox" id="useCustomUA">
                    <span class="setting-label">使用自定义UA</span>
                </div>
                <textarea id="customUA" placeholder="输入自定义User-Agent字符串..." class="textarea-input" disabled></textarea>
            </div>

            <!-- 当前User-Agent显示 -->
            <div class="setting-group">
                <label class="group-label">当前User-Agent:</label>
                <div id="currentUA" class="current-ua"></div>
            </div>

            <!-- 域名设置 -->
            <div class="setting-group">
                <label class="group-label">域名设置:</label>
                <div class="domain-settings">
                    <div class="setting-item">
                        <input type="radio" id="allDomains" name="domainMode" value="all" checked>
                        <label for="allDomains">所有网站</label>
                    </div>
                    <div class="setting-item">
                        <input type="radio" id="specificDomains" name="domainMode" value="specific">
                        <label for="specificDomains">指定网站</label>
                    </div>
                </div>
                <textarea id="targetDomains" placeholder="输入域名，每行一个&#10;例如：&#10;example.com&#10;test.org" class="textarea-input small" disabled></textarea>
            </div>

            <!-- 排除域名 -->
            <div class="setting-group">
                <label for="excludeDomains" class="group-label">排除域名:</label>
                <textarea id="excludeDomains" placeholder="输入要排除的域名，每行一个&#10;例如：&#10;google.com&#10;github.com" class="textarea-input small"></textarea>
            </div>

            <!-- 操作按钮 -->
            <div class="button-group">
                <button id="saveBtn" class="btn btn-primary">保存设置</button>
                <button id="resetBtn" class="btn btn-secondary">重置</button>
                <button id="testBtn" class="btn btn-test">测试UA</button>
            </div>

            <!-- 状态显示 -->
            <div id="status" class="status"></div>
        </div>
    </div>

    <script src="popup.js"></script>
</body>
</html>
