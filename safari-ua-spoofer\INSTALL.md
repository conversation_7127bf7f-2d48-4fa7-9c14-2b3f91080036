# Safari iPhone UA Spoofer 安装指南

## 快速安装

### 1. 准备工作
确保你使用的是Chrome浏览器或基于Chromium的浏览器（如Edge、Brave等）。

### 2. 安装步骤

1. **打开扩展程序管理页面**
   - 在Chrome地址栏输入：`chrome://extensions/`
   - 或者点击右上角三点菜单 → 更多工具 → 扩展程序

2. **开启开发者模式**
   - 在扩展程序页面右上角，开启"开发者模式"开关

3. **加载插件**
   - 点击"加载已解压的扩展程序"按钮
   - 选择 `safari-ua-spoofer` 文件夹
   - 点击"选择文件夹"

4. **确认安装**
   - 插件应该出现在扩展程序列表中
   - 浏览器工具栏会显示插件图标

### 3. 首次配置

1. **打开插件设置**
   - 点击浏览器工具栏中的插件图标
   - 或者在扩展程序页面点击"详细信息" → "扩展程序选项"

2. **基本设置**
   - 确保"启用UA伪装"开关已开启
   - 选择要伪装的iPhone型号（默认：iPhone 15 Pro）
   - 点击"保存设置"

3. **测试效果**
   - 点击"测试UA"按钮打开测试页面
   - 或者直接访问：`https://httpbin.org/user-agent`
   - 检查User-Agent是否已成功伪装

## 高级配置

### 域名设置

**所有网站模式（默认）**
- 对所有访问的网站都应用UA伪装
- 适合大多数使用场景

**指定网站模式**
- 只对特定网站应用UA伪装
- 在"指定网站"文本框中输入域名，每行一个
- 例如：
  ```
  example.com
  test.org
  mobile.site.com
  ```

### 排除域名

如果某些网站不希望使用UA伪装，可以在"排除域名"中添加：
```
google.com
github.com
localhost
```

### 自定义User-Agent

1. 勾选"使用自定义UA"
2. 在文本框中输入自定义的User-Agent字符串
3. 点击"保存设置"

## 故障排除

### 插件无法加载
- 检查文件夹路径是否正确
- 确保所有必需文件都存在
- 尝试重新加载插件

### UA伪装不生效
- 确保插件已启用
- 检查域名设置是否正确
- 尝试刷新页面或重启浏览器
- 查看浏览器控制台是否有错误信息

### 某些网站检测到伪装
- 现代网站可能有多种检测机制
- 可以尝试不同的iPhone型号
- 考虑使用自定义UA字符串

## 卸载插件

1. 进入 `chrome://extensions/`
2. 找到"Safari iPhone UA Spoofer"
3. 点击"移除"按钮
4. 确认删除

## 注意事项

- 此插件仅用于开发测试目的
- 请遵守网站使用条款和相关法律法规
- 某些网站可能有其他检测机制
- 建议在测试环境中使用

## 技术支持

如果遇到问题，可以：
1. 查看浏览器控制台错误信息
2. 检查插件的background页面日志
3. 使用测试页面验证功能是否正常
