<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UA头移除测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>UA头移除扩展测试页面</h1>
        
        <div class="test-section">
            <h3>测试1: 检查当前页面的User-Agent</h3>
            <p>JavaScript获取的User-Agent（这个不会被扩展影响）:</p>
            <div class="result" id="jsUserAgent"></div>
        </div>

        <div class="test-section">
            <h3>测试2: 发送AJAX请求测试</h3>
            <button onclick="testAjaxRequest()">测试AJAX请求</button>
            <div class="result" id="ajaxResult"></div>
        </div>

        <div class="test-section">
            <h3>测试3: 发送Fetch请求测试</h3>
            <button onclick="testFetchRequest()">测试Fetch请求</button>
            <div class="result" id="fetchResult"></div>
        </div>

        <div class="test-section">
            <h3>测试4: 新窗口/新标签页测试</h3>
            <button onclick="openNewWindow()">在新窗口中打开测试页面</button>
            <button onclick="openNewTab()">在新标签页中打开测试页面</button>
            <p><strong>说明：</strong>点击按钮后会打开新的窗口/标签页，在新页面中再次运行测试来验证扩展是否在新页面中正常工作。</p>
        </div>

        <div class="test-section">
            <h3>测试5: 模拟链接点击</h3>
            <a href="https://httpbin.org/headers" target="_blank" onclick="logLinkClick(this)">点击此链接在新标签页中查看请求头</a>
            <p><strong>说明：</strong>这个链接会在新标签页中打开httpbin.org，你可以直接查看返回的JSON来验证是否包含User-Agent。</p>
        </div>

        <div class="test-section">
            <h3>测试说明</h3>
            <ul>
                <li><strong>如果扩展工作正常</strong>：AJAX和Fetch请求返回的headers中应该<span class="success">没有User-Agent</span></li>
                <li><strong>如果扩展未工作</strong>：请求返回的headers中会<span class="warning">包含User-Agent</span></li>
                <li>JavaScript获取的User-Agent不会被影响，这是正常的</li>
                <li>建议同时打开开发者工具的Network标签页查看实际的网络请求</li>
            </ul>
        </div>
    </div>

    <script>
        // 显示JavaScript获取的User-Agent
        document.getElementById('jsUserAgent').textContent = navigator.userAgent;

        // 测试AJAX请求
        function testAjaxRequest() {
            const resultDiv = document.getElementById('ajaxResult');
            resultDiv.textContent = '正在测试...';
            
            const xhr = new XMLHttpRequest();
            xhr.open('GET', 'https://httpbin.org/headers', true);
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    if (xhr.status === 200) {
                        try {
                            const response = JSON.parse(xhr.responseText);
                            const headers = response.headers;
                            
                            if (headers['User-Agent']) {
                                resultDiv.innerHTML = '<span class="warning">⚠️ 检测到User-Agent头部:</span>\n' + 
                                                    JSON.stringify(headers, null, 2);
                            } else {
                                resultDiv.innerHTML = '<span class="success">✅ 成功！没有检测到User-Agent头部</span>\n' + 
                                                    JSON.stringify(headers, null, 2);
                            }
                        } catch (e) {
                            resultDiv.textContent = '解析响应失败: ' + e.message;
                        }
                    } else {
                        resultDiv.textContent = '请求失败: ' + xhr.status;
                    }
                }
            };
            xhr.send();
        }

        // 测试Fetch请求
        function testFetchRequest() {
            const resultDiv = document.getElementById('fetchResult');
            resultDiv.textContent = '正在测试...';

            fetch('https://httpbin.org/headers')
                .then(response => response.json())
                .then(data => {
                    const headers = data.headers;

                    if (headers['User-Agent']) {
                        resultDiv.innerHTML = '<span class="warning">⚠️ 检测到User-Agent头部:</span>\n' +
                                            JSON.stringify(headers, null, 2);
                    } else {
                        resultDiv.innerHTML = '<span class="success">✅ 成功！没有检测到User-Agent头部</span>\n' +
                                            JSON.stringify(headers, null, 2);
                    }
                })
                .catch(error => {
                    resultDiv.textContent = '请求失败: ' + error.message;
                });
        }

        // 在新窗口中打开测试页面
        function openNewWindow() {
            const currentUrl = window.location.href;
            window.open(currentUrl, '_blank', 'width=800,height=600');
        }

        // 在新标签页中打开测试页面
        function openNewTab() {
            const currentUrl = window.location.href;
            window.open(currentUrl, '_blank');
        }

        // 记录链接点击
        function logLinkClick(link) {
            console.log('点击链接:', link.href);
            console.log('当前时间:', new Date().toISOString());
        }
    </script>
</body>
</html>
