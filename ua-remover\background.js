// Background script for UA Header Remover/Spoofer extension
// This extension can remove or spoof User-Agent headers for specific domains

// 读取UA配置并更新规则
async function loadConfigAndUpdateRules() {
  try {
    // 优先读取新的配置文件
    let config;
    try {
      const response = await fetch(chrome.runtime.getURL('ua-config.json'));
      config = await response.json();
    } catch (e) {
      // 如果新配置文件不存在，回退到旧配置
      const response = await fetch(chrome.runtime.getURL('domains.json'));
      const oldConfig = await response.json();
      config = {
        mode: 'remove',
        targetDomains: oldConfig.targetDomains || [],
        userAgents: {},
        defaultUA: 'chrome_windows',
        randomize: false
      };
    }

    const targetDomains = config.targetDomains || [];
    const mode = config.mode || 'remove';
    currentDomains = targetDomains; // 保存到全局变量

    console.log('Target domains loaded:', targetDomains);
    console.log('Operation mode:', mode);

    // 清除旧规则并添加新规则
    await chrome.declarativeNetRequest.updateDynamicRules({
      removeRuleIds: [1, 2, 3], // 清除可能存在的旧规则
      addRules: [
        {
          "id": 1,
          "priority": 100,
          "action": {
            "type": "modifyHeaders",
            "requestHeaders": [
              {
                "header": "User-Agent",
                "operation": "remove"
              }
            ]
          },
          "condition": {
            "requestDomains": targetDomains,
            "resourceTypes": [
              "main_frame",
              "sub_frame",
              "stylesheet",
              "script",
              "image",
              "font",
              "object",
              "xmlhttprequest",
              "ping",
              "csp_report",
              "media",
              "websocket",
              "webtransport",
              "webbundle",
              "other"
            ]
          }
        },
        {
          "id": 2,
          "priority": 100,
          "action": {
            "type": "modifyHeaders",
            "requestHeaders": [
              {
                "header": "user-agent",
                "operation": "remove"
              }
            ]
          },
          "condition": {
            "requestDomains": targetDomains,
            "resourceTypes": [
              "main_frame",
              "sub_frame",
              "stylesheet",
              "script",
              "image",
              "font",
              "object",
              "xmlhttprequest",
              "ping",
              "csp_report",
              "media",
              "websocket",
              "webtransport",
              "webbundle",
              "other"
            ]
          }
        },
        {
          "id": 3,
          "priority": 100,
          "action": {
            "type": "modifyHeaders",
            "requestHeaders": [
              {
                "header": "User-agent",
                "operation": "remove"
              }
            ]
          },
          "condition": {
            "requestDomains": targetDomains,
            "resourceTypes": [
              "main_frame",
              "sub_frame",
              "stylesheet",
              "script",
              "image",
              "font",
              "object",
              "xmlhttprequest",
              "ping",
              "csp_report",
              "media",
              "websocket",
              "webtransport",
              "webbundle",
              "other"
            ]
          }
        }
      ]
    });

    console.log('Dynamic rules updated successfully for domains:', targetDomains);
  } catch (error) {
    console.error('Failed to load domains or update rules:', error);
  }
}

// 存储当前的域名配置
let currentDomains = [];

chrome.runtime.onInstalled.addListener(() => {
  console.log('UA Header Remover extension installed');
  loadConfigAndUpdateRules();
});

// 监听扩展启动
chrome.runtime.onStartup.addListener(() => {
  console.log('UA Header Remover extension started');
  loadConfigAndUpdateRules();
});

// 处理来自content script的消息
chrome.runtime.onMessage.addListener((request, _sender, sendResponse) => {
  if (request.action === 'getDomains') {
    sendResponse({domains: currentDomains});
  }
});

// 添加调试信息
chrome.declarativeNetRequest.onRuleMatchedDebug.addListener((info) => {
  console.log('Rule matched:', info);
});
