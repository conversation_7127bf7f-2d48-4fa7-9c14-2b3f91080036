<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>特定域名UA移除测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 900px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .result {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            margin-top: 10px;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .warning {
            color: #ffc107;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .domain-list {
            background: #e9ecef;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 3px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>特定域名UA头移除测试</h1>
        
        <div class="test-section">
            <h3>当前配置的目标域名</h3>
            <div class="domain-list" id="domainList">加载中...</div>
            <button onclick="loadDomains()">刷新域名列表</button>
        </div>

        <div class="test-section">
            <h3>测试不同域名的请求</h3>
            <div>
                <label>测试域名: </label>
                <input type="text" id="testDomain" value="httpbin.org" placeholder="输入要测试的域名">
                <button onclick="testSpecificDomain()">测试此域名</button>
            </div>
            <div class="result" id="domainTestResult"></div>
        </div>

        <div class="test-section">
            <h3>预设域名测试</h3>
            <button onclick="testDomain('httpbin.org')">测试 httpbin.org (应该移除UA)</button>
            <button onclick="testDomain('google.com')">测试 google.com (应该保留UA)</button>
            <button onclick="testDomain('example.com')">测试 example.com (应该移除UA)</button>
            <div class="result" id="presetTestResult"></div>
        </div>

        <div class="test-section">
            <h3>批量对比测试</h3>
            <button onclick="runBatchTest()">运行批量测试</button>
            <div class="result" id="batchTestResult"></div>
        </div>

        <div class="test-section">
            <h3>使用说明</h3>
            <ul>
                <li><strong>绿色✅</strong>：表示成功移除了User-Agent头（仅对目标域名）</li>
                <li><strong>黄色⚠️</strong>：表示检测到User-Agent头（非目标域名的正常行为）</li>
                <li><strong>红色❌</strong>：表示请求失败或配置错误</li>
                <li>修改域名配置请编辑 <code>domains.json</code> 文件</li>
                <li>修改后需要重新加载扩展才能生效</li>
            </ul>
        </div>
    </div>

    <script>
        let targetDomains = [];

        // 加载域名配置
        async function loadDomains() {
            const resultDiv = document.getElementById('domainList');
            try {
                const response = await fetch(chrome.runtime.getURL('domains.json'));
                const config = await response.json();
                targetDomains = config.targetDomains || [];
                
                resultDiv.innerHTML = `
                    <strong>目标域名列表：</strong><br>
                    ${targetDomains.map(domain => `• ${domain}`).join('<br>')}
                    <br><br>
                    <small>总共 ${targetDomains.length} 个域名</small>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 加载域名配置失败: ${error.message}</span>`;
            }
        }

        // 测试特定域名
        async function testSpecificDomain() {
            const domain = document.getElementById('testDomain').value.trim();
            if (!domain) {
                alert('请输入要测试的域名');
                return;
            }
            await testDomain(domain, 'domainTestResult');
        }

        // 测试域名函数
        async function testDomain(domain, resultElementId = 'presetTestResult') {
            const resultDiv = document.getElementById(resultElementId);
            resultDiv.textContent = `正在测试 ${domain}...`;
            
            const testUrl = `https://${domain}/headers`;
            const isTargetDomain = targetDomains.some(target => 
                domain === target || domain.endsWith('.' + target)
            );
            
            try {
                const response = await fetch(testUrl);
                const data = await response.json();
                const headers = data.headers || {};
                
                const hasUA = headers['User-Agent'] || headers['user-agent'] || headers['User-agent'];
                
                let status, message;
                if (isTargetDomain) {
                    // 目标域名应该没有UA头
                    if (hasUA) {
                        status = 'warning';
                        message = `⚠️ 目标域名 ${domain} 仍然包含User-Agent头，扩展可能未生效`;
                    } else {
                        status = 'success';
                        message = `✅ 目标域名 ${domain} 成功移除User-Agent头`;
                    }
                } else {
                    // 非目标域名应该保留UA头
                    if (hasUA) {
                        status = 'success';
                        message = `✅ 非目标域名 ${domain} 正常保留User-Agent头`;
                    } else {
                        status = 'warning';
                        message = `⚠️ 非目标域名 ${domain} 意外丢失User-Agent头`;
                    }
                }
                
                resultDiv.innerHTML = `
                    <span class="${status}">${message}</span><br>
                    <strong>域名:</strong> ${domain} (${isTargetDomain ? '目标域名' : '非目标域名'})<br>
                    <strong>响应头:</strong><br>
                    ${JSON.stringify(headers, null, 2)}
                `;
                
            } catch (error) {
                resultDiv.innerHTML = `<span class="error">❌ 测试 ${domain} 失败: ${error.message}</span>`;
            }
        }

        // 批量测试
        async function runBatchTest() {
            const resultDiv = document.getElementById('batchTestResult');
            resultDiv.textContent = '正在运行批量测试...';
            
            const testDomains = [
                'httpbin.org',    // 目标域名
                'example.com',    // 目标域名
                'google.com',     // 非目标域名
                'github.com',     // 非目标域名
                'stackoverflow.com' // 非目标域名
            ];
            
            let results = [];
            
            for (const domain of testDomains) {
                try {
                    const testUrl = `https://${domain}/headers`;
                    const isTargetDomain = targetDomains.some(target => 
                        domain === target || domain.endsWith('.' + target)
                    );
                    
                    const response = await fetch(testUrl);
                    const data = await response.json();
                    const headers = data.headers || {};
                    const hasUA = headers['User-Agent'] || headers['user-agent'] || headers['User-agent'];
                    
                    let status;
                    if (isTargetDomain) {
                        status = hasUA ? '⚠️ 失败' : '✅ 成功';
                    } else {
                        status = hasUA ? '✅ 正常' : '⚠️ 异常';
                    }
                    
                    results.push(`${status} ${domain} (${isTargetDomain ? '目标' : '非目标'}) - UA: ${hasUA ? '存在' : '不存在'}`);
                    
                } catch (error) {
                    results.push(`❌ ${domain} - 测试失败: ${error.message}`);
                }
                
                // 添加延迟避免请求过快
                await new Promise(resolve => setTimeout(resolve, 500));
            }
            
            resultDiv.innerHTML = results.join('<br>');
        }

        // 页面加载时自动加载域名配置
        document.addEventListener('DOMContentLoaded', loadDomains);
    </script>
</body>
</html>
