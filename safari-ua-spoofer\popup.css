/* Safari iPhone UA Spoofer Popup Styles */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-size: 14px;
    line-height: 1.4;
    color: #333;
    background: #f8f9fa;
}

.container {
    width: 380px;
    min-height: 500px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header {
    background: linear-gradient(135deg, #007AFF, #5856D6);
    color: white;
    padding: 20px;
    text-align: center;
    border-radius: 8px 8px 0 0;
}

.header h1 {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 5px;
}

.header p {
    font-size: 12px;
    opacity: 0.9;
}

.main-content {
    padding: 20px;
}

.setting-group {
    margin-bottom: 20px;
}

.group-label {
    display: block;
    font-weight: 500;
    margin-bottom: 8px;
    color: #555;
}

.setting-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.setting-label {
    margin-left: 10px;
    font-size: 14px;
}

/* 开关样式 */
.switch {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 24px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #007AFF;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* 输入框样式 */
.select-input, .textarea-input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 13px;
    transition: border-color 0.3s;
}

.select-input:focus, .textarea-input:focus {
    outline: none;
    border-color: #007AFF;
    box-shadow: 0 0 0 2px rgba(0, 122, 255, 0.1);
}

.textarea-input {
    resize: vertical;
    min-height: 80px;
    font-family: monospace;
}

.textarea-input.small {
    min-height: 60px;
}

.textarea-input:disabled {
    background-color: #f5f5f5;
    color: #999;
}

/* 当前UA显示 */
.current-ua {
    background: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 6px;
    padding: 10px;
    font-family: monospace;
    font-size: 11px;
    word-break: break-all;
    max-height: 80px;
    overflow-y: auto;
    color: #666;
}

/* 域名设置 */
.domain-settings {
    margin-bottom: 10px;
}

.domain-settings .setting-item {
    margin-bottom: 5px;
}

.domain-settings input[type="radio"] {
    margin-right: 8px;
}

/* 按钮样式 */
.button-group {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.btn {
    flex: 1;
    padding: 10px 16px;
    border: none;
    border-radius: 6px;
    font-size: 13px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-primary {
    background: #007AFF;
    color: white;
}

.btn-primary:hover {
    background: #0056CC;
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-test {
    background: #28a745;
    color: white;
}

.btn-test:hover {
    background: #1e7e34;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

/* 状态显示 */
.status {
    margin-top: 15px;
    padding: 10px;
    border-radius: 6px;
    font-size: 12px;
    text-align: center;
    display: none;
}

.status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.status.info {
    background: #d1ecf1;
    color: #0c5460;
    border: 1px solid #bee5eb;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}
